<?php

namespace App\Controllers;

use App\Models\ExercisePositionsPreScreenModel;
use App\Models\PositionsModel;
use App\Models\PositionsGroupModel;
use App\Models\ExerciseModel;

class ExercisePrescreeningPositionsController extends BaseController
{
    protected $exercisePositionsPreScreenModel;
    protected $positionsModel;
    protected $positionsGroupModel;
    protected $exerciseModel;
    protected $session;

    public function __construct()
    {
        helper(['form', 'url']);
        $this->exercisePositionsPreScreenModel = new ExercisePositionsPreScreenModel();
        $this->positionsModel = new PositionsModel();
        $this->positionsGroupModel = new PositionsGroupModel();
        $this->exerciseModel = new ExerciseModel();
        $this->session = session();
    }

    /**
     * [GET] Display pre-screening positions management page
     * URI: /exercise_prescreening_positions/(:num)
     */
    public function index($exerciseId)
    {
        // Get organization ID from session
        $orgId = $this->session->get('org_id');
        if (!$orgId) {
            $this->session->setFlashdata('error', 'Organization context not found.');
            return redirect()->to(base_url('exercises'));
        }

        // Get exercise data
        $exercise = $this->exerciseModel->find($exerciseId);
        if (!$exercise) {
            $this->session->setFlashdata('error', 'Exercise not found.');
            return redirect()->to(base_url('exercises'));
        }

        // Verify exercise belongs to current organization
        if ($exercise['org_id'] != $orgId) {
            $this->session->setFlashdata('error', 'Access denied. Exercise does not belong to your organization.');
            return redirect()->to(base_url('exercises'));
        }

        // Get all positions for this exercise with their groups
        $allPositions = $this->positionsModel->select('
                positions.*,
                positions_groups.group_name
            ')
            ->join('positions_groups', 'positions.position_group_id = positions_groups.id', 'left')
            ->where('positions_groups.exercise_id', $exerciseId)
            ->where('positions.deleted_at IS NULL')
            ->orderBy('positions_groups.group_name', 'ASC')
            ->orderBy('positions.designation', 'ASC')
            ->findAll();

        // Get positions already marked for pre-screening
        $preScreeningPositions = $this->exercisePositionsPreScreenModel->select('
                exercise_positions_pre_screen.*,
                positions.designation,
                positions.position_reference,
                positions_groups.group_name
            ')
            ->join('positions', 'exercise_positions_pre_screen.position_id = positions.id', 'left')
            ->join('positions_groups', 'positions.position_group_id = positions_groups.id', 'left')
            ->where('exercise_positions_pre_screen.org_id', $orgId)
            ->where('exercise_positions_pre_screen.exercise_id', $exerciseId)
            ->orderBy('positions_groups.group_name', 'ASC')
            ->orderBy('positions.designation', 'ASC')
            ->findAll();

        // Create array of pre-screening position IDs for easy checking
        $preScreeningPositionIds = array_column($preScreeningPositions, 'position_id');

        // Filter out positions that are already in pre-screening from all positions
        $availablePositions = array_filter($allPositions, function($position) use ($preScreeningPositionIds) {
            return !in_array($position['id'], $preScreeningPositionIds);
        });

        $data = [
            'title' => 'Pre-Screening Positions - ' . $exercise['exercise_name'],
            'menu' => 'exercises',
            'exercise' => $exercise,
            'availablePositions' => $availablePositions,
            'preScreeningPositions' => $preScreeningPositions
        ];

        return view('exercise_prescreening_positions/exercise_prescreening_positions_index', $data);
    }

    /**
     * [POST] Add position to pre-screening
     * URI: /exercise_prescreening_positions/add
     */
    public function add()
    {
        // Get organization ID from session
        $orgId = $this->session->get('org_id');
        $userId = $this->session->get('user_id');
        
        if (!$orgId || !$userId) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Session context not found.'
            ]);
        }

        $exerciseId = $this->request->getPost('exercise_id');
        $positionId = $this->request->getPost('position_id');

        // Validate input
        if (!$exerciseId || !$positionId) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Exercise ID and Position ID are required.'
            ]);
        }

        // Check if combination already exists
        if ($this->exercisePositionsPreScreenModel->combinationExists($orgId, $exerciseId, $positionId)) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Position is already marked for pre-screening.'
            ]);
        }

        // Prepare data for insertion
        $data = [
            'org_id' => $orgId,
            'exercise_id' => $exerciseId,
            'position_id' => $positionId,
            'created_by' => $userId
        ];

        // Insert the record
        if ($this->exercisePositionsPreScreenModel->insert($data)) {
            return $this->response->setJSON([
                'success' => true,
                'message' => 'Position added to pre-screening successfully.'
            ]);
        } else {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Failed to add position to pre-screening.'
            ]);
        }
    }

    /**
     * [POST] Remove position from pre-screening
     * URI: /exercise_prescreening_positions/remove
     */
    public function remove()
    {
        // Get organization ID from session
        $orgId = $this->session->get('org_id');
        
        if (!$orgId) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Organization context not found.'
            ]);
        }

        $exerciseId = $this->request->getPost('exercise_id');
        $positionId = $this->request->getPost('position_id');

        // Validate input
        if (!$exerciseId || !$positionId) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Exercise ID and Position ID are required.'
            ]);
        }

        // Delete the record
        if ($this->exercisePositionsPreScreenModel->deleteByCombination($orgId, $exerciseId, $positionId)) {
            return $this->response->setJSON([
                'success' => true,
                'message' => 'Position removed from pre-screening successfully.'
            ]);
        } else {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Failed to remove position from pre-screening.'
            ]);
        }
    }
}
