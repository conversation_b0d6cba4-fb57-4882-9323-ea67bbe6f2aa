<?php
/**
 * Exercise Pre-screening Positions Index View
 * Displays positions management for pre-screening selection
 */
?>

<?= $this->extend('templates/nolstemp') ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <div class="row mb-3">
        <div class="col-md-8">
            <h2><i class="fas fa-list-check me-2"></i>Pre-Screening Positions</h2>
            <p class="text-muted">Select which positions must go through pre-screening for this exercise</p>
        </div>
        <div class="col-md-4 text-end">
            <a href="<?= base_url('exercise_settings/' . $exercise['id']) ?>" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-1"></i> Back to Settings
            </a>
        </div>
    </div>

    <!-- Exercise Information Card -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-info-circle me-2"></i>Exercise Information
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <p><strong>Exercise Name:</strong> <?= esc($exercise['exercise_name']) ?></p>
                            <p><strong>Advertisement No:</strong> <?= esc($exercise['advertisement_no'] ?? 'N/A') ?></p>
                        </div>
                        <div class="col-md-6">
                            <p><strong>Status:</strong> 
                                <span class="badge <?= $exercise['status'] === 'draft' ? 'bg-secondary' : ($exercise['status'] === 'publish' ? 'bg-success' : ($exercise['status'] === 'selection' ? 'bg-primary' : 'bg-danger')) ?>">
                                    <?= ucfirst(esc($exercise['status'])) ?>
                                </span>
                            </p>
                            <p><strong>Organization:</strong> <?= esc($exercise['org_id']) ?></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Two Tables Layout -->
    <div class="row">
        <!-- Left Table - Available Positions -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-header bg-warning text-dark">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-list me-2"></i>Available Positions
                        <span class="badge bg-dark ms-2"><?= count($availablePositions) ?></span>
                    </h5>
                </div>
                <div class="card-body">
                    <?php if (!empty($availablePositions)): ?>
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead class="table-dark">
                                    <tr>
                                        <th>Group</th>
                                        <th>Position</th>
                                        <th>Reference</th>
                                        <th>Action</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($availablePositions as $position): ?>
                                        <tr>
                                            <td>
                                                <small class="text-muted"><?= esc($position['group_name'] ?? 'No Group') ?></small>
                                            </td>
                                            <td>
                                                <strong><?= esc($position['designation']) ?></strong>
                                                <br>
                                                <small class="text-muted"><?= esc($position['classification']) ?></small>
                                            </td>
                                            <td>
                                                <span class="badge bg-info"><?= esc($position['position_reference']) ?></span>
                                            </td>
                                            <td>
                                                <button type="button" 
                                                        class="btn btn-sm btn-success add-position-btn" 
                                                        data-position-id="<?= $position['id'] ?>"
                                                        data-position-name="<?= esc($position['designation']) ?>"
                                                        title="Add to Pre-screening">
                                                    <i class="fas fa-plus me-1"></i> Add
                                                </button>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php else: ?>
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            All positions are already marked for pre-screening, or no positions are available for this exercise.
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Right Table - Pre-screening Positions -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-header bg-success text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-check-circle me-2"></i>Pre-screening Positions
                        <span class="badge bg-light text-dark ms-2"><?= count($preScreeningPositions) ?></span>
                    </h5>
                </div>
                <div class="card-body">
                    <?php if (!empty($preScreeningPositions)): ?>
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead class="table-dark">
                                    <tr>
                                        <th>Group</th>
                                        <th>Position</th>
                                        <th>Reference</th>
                                        <th>Action</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($preScreeningPositions as $position): ?>
                                        <tr>
                                            <td>
                                                <small class="text-muted"><?= esc($position['group_name'] ?? 'No Group') ?></small>
                                            </td>
                                            <td>
                                                <strong><?= esc($position['designation']) ?></strong>
                                                <br>
                                                <small class="text-success">
                                                    <i class="fas fa-clock me-1"></i>
                                                    Added: <?= date('M j, Y', strtotime($position['created_at'])) ?>
                                                </small>
                                            </td>
                                            <td>
                                                <span class="badge bg-info"><?= esc($position['position_reference']) ?></span>
                                            </td>
                                            <td>
                                                <button type="button" 
                                                        class="btn btn-sm btn-danger remove-position-btn" 
                                                        data-position-id="<?= $position['position_id'] ?>"
                                                        data-position-name="<?= esc($position['designation']) ?>"
                                                        title="Remove from Pre-screening">
                                                    <i class="fas fa-times me-1"></i> Remove
                                                </button>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php else: ?>
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            No positions are currently marked for pre-screening. Add positions from the left table.
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
$(document).ready(function() {
    const exerciseId = <?= $exercise['id'] ?>;
    
    // Add position to pre-screening
    $('.add-position-btn').click(function() {
        const positionId = $(this).data('position-id');
        const positionName = $(this).data('position-name');
        const button = $(this);
        
        if (confirm(`Are you sure you want to add "${positionName}" to pre-screening?`)) {
            button.prop('disabled', true).html('<i class="fas fa-spinner fa-spin me-1"></i> Adding...');
            
            $.post('<?= base_url('exercise_prescreening_positions/add') ?>', {
                exercise_id: exerciseId,
                position_id: positionId
            })
            .done(function(response) {
                if (response.success) {
                    toastr.success(response.message);
                    // Reload page to update both tables
                    setTimeout(() => {
                        location.reload();
                    }, 1000);
                } else {
                    toastr.error(response.message);
                    button.prop('disabled', false).html('<i class="fas fa-plus me-1"></i> Add');
                }
            })
            .fail(function() {
                toastr.error('Failed to add position to pre-screening.');
                button.prop('disabled', false).html('<i class="fas fa-plus me-1"></i> Add');
            });
        }
    });
    
    // Remove position from pre-screening
    $('.remove-position-btn').click(function() {
        const positionId = $(this).data('position-id');
        const positionName = $(this).data('position-name');
        const button = $(this);
        
        if (confirm(`Are you sure you want to remove "${positionName}" from pre-screening?`)) {
            button.prop('disabled', true).html('<i class="fas fa-spinner fa-spin me-1"></i> Removing...');
            
            $.post('<?= base_url('exercise_prescreening_positions/remove') ?>', {
                exercise_id: exerciseId,
                position_id: positionId
            })
            .done(function(response) {
                if (response.success) {
                    toastr.success(response.message);
                    // Reload page to update both tables
                    setTimeout(() => {
                        location.reload();
                    }, 1000);
                } else {
                    toastr.error(response.message);
                    button.prop('disabled', false).html('<i class="fas fa-times me-1"></i> Remove');
                }
            })
            .fail(function() {
                toastr.error('Failed to remove position from pre-screening.');
                button.prop('disabled', false).html('<i class="fas fa-times me-1"></i> Remove');
            });
        }
    });
});
</script>
<?= $this->endSection() ?>
