<?php

namespace App\Models;

use CodeIgniter\Model;

class ExercisePositionsPreScreenModel extends Model
{
    protected $table = 'exercise_positions_pre_screen';
    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType = 'array';
    protected $useSoftDeletes = false;
    protected $protectFields = true;

    // Fields that are allowed to be set during insert/update operations.
    protected $allowedFields = [
        'org_id',
        'exercise_id',
        'position_id',
        'created_by'
    ];

    // Dates
    protected $useTimestamps = true;
    protected $dateFormat = 'datetime';
    protected $createdField = 'created_at';
    protected $updatedField = null; // No updated_at field in the table

    // Validation
    protected $validationRules = [
        'org_id' => 'required|numeric',
        'exercise_id' => 'required|numeric',
        'position_id' => 'required|numeric',
        'created_by' => 'required|numeric'
    ];

    protected $validationMessages = [
        'org_id' => [
            'required' => 'Organization ID is required',
            'numeric' => 'Organization ID must be a number'
        ],
        'exercise_id' => [
            'required' => 'Exercise ID is required',
            'numeric' => 'Exercise ID must be a number'
        ],
        'position_id' => [
            'required' => 'Position ID is required',
            'numeric' => 'Position ID must be a number'
        ],
        'created_by' => [
            'required' => 'Created by is required',
            'numeric' => 'Created by must be a number'
        ]
    ];

    protected $skipValidation = false;
    protected $cleanValidationRules = true;

    /**
     * Get exercise positions pre-screen records by organization ID
     *
     * @param int $orgId
     * @return array
     */
    public function getByOrgId($orgId)
    {
        return $this->where('org_id', $orgId)->orderBy('created_at', 'DESC')->findAll();
    }

    /**
     * Get exercise positions pre-screen records by exercise ID
     *
     * @param int $exerciseId
     * @return array
     */
    public function getByExerciseId($exerciseId)
    {
        return $this->where('exercise_id', $exerciseId)->orderBy('created_at', 'DESC')->findAll();
    }

    /**
     * Get exercise positions pre-screen records by position ID
     *
     * @param int $positionId
     * @return array
     */
    public function getByPositionId($positionId)
    {
        return $this->where('position_id', $positionId)->orderBy('created_at', 'DESC')->findAll();
    }

    /**
     * Get exercise positions pre-screen records with related data
     *
     * @param int $orgId Optional organization filter
     * @return array
     */
    public function getWithRelatedData($orgId = null)
    {
        $builder = $this->select('
            exercise_positions_pre_screen.*,
            exercises.exercise_name,
            exercises.advertisement_no,
            positions.designation,
            positions.position_reference,
            dakoii_org.org_name,
            dakoii_org.org_code,
            users.name as created_by_name
        ')
        ->join('exercises', 'exercise_positions_pre_screen.exercise_id = exercises.id', 'left')
        ->join('positions', 'exercise_positions_pre_screen.position_id = positions.id', 'left')
        ->join('dakoii_org', 'exercise_positions_pre_screen.org_id = dakoii_org.id', 'left')
        ->join('users', 'exercise_positions_pre_screen.created_by = users.id', 'left')
        ->orderBy('exercise_positions_pre_screen.created_at', 'DESC');

        if ($orgId !== null) {
            $builder->where('exercise_positions_pre_screen.org_id', $orgId);
        }

        return $builder->findAll();
    }

    /**
     * Check if a combination already exists
     *
     * @param int $orgId
     * @param int $exerciseId
     * @param int $positionId
     * @return bool
     */
    public function combinationExists($orgId, $exerciseId, $positionId)
    {
        $result = $this->where([
            'org_id' => $orgId,
            'exercise_id' => $exerciseId,
            'position_id' => $positionId
        ])->first();

        return $result !== null;
    }

    /**
     * Get unique exercise-position combinations for an organization
     *
     * @param int $orgId
     * @return array
     */
    public function getUniqueExercisePositions($orgId)
    {
        return $this->select('DISTINCT exercise_id, position_id')
            ->where('org_id', $orgId)
            ->findAll();
    }

    /**
     * Delete by combination
     *
     * @param int $orgId
     * @param int $exerciseId
     * @param int $positionId
     * @return bool
     */
    public function deleteByCombination($orgId, $exerciseId, $positionId)
    {
        return $this->where([
            'org_id' => $orgId,
            'exercise_id' => $exerciseId,
            'position_id' => $positionId
        ])->delete();
    }
}
